const {r} = require("../../libs/ajax.js")
var jie = require('../../libs/aesjie');
let videoAd = ""
Page({

  data: {

  },

  onLoad(e) {
    wx.getStorageSync('img')
    this.setData({img:wx.getStorageSync('img').img})

    console.log(wx.getStorageSync("cs"));

    if(wx.createRewardedVideoAd){
      videoAd = wx.createRewardedVideoAd({
        adUnitId: wx.getStorageSync("data").gg
      })
      videoAd.onError(err => {
        this.gxgn()
      })
      videoAd.onClose((status) => {
        if (status && status.isEnded || status === undefined) {
          if(this.data.qz==true){
            if(this.data.index==1){
              this.gxgn()
              wx.setStorageSync('cs',1)
            }else{
              wx.showModal({
                title: '获取失败',
                content: '未点击视频内绿色按钮!',
                cancelText:'不要啦',
                confirmText:'继续操作',
                confirmColor:'#ff9988',
                complete: (res) => {
                  if (res.confirm) {
                    this.kgg()
                  }
                  if (res.cancel) {
                    wx.showToast({
                      title: '取消操作',
                      icon:'error'
                    })
                  }
                }
              })
            }
          }else{
            this.gxgn()
          }
          
           
        }else{
          wx.showModal({
            title: '观看失败',
            content: '中途退出观看 不可保存o!',
            cancelText:'不要啦',
            confirmText:'继续观看',
            confirmColor:'#ff9988',
            complete: (res) => {
              if (res.confirm) {
                this.kgg()
              }
              if (res.cancel) {
                wx.showToast({
                  title: '保存失败',
                  icon:'error'
                })
              }
            }
          })
        }
      },(err)=>{
        this.gxgn()
      })
    }
  },
  lxkf(){
    wx.showModal({
      title: '提示',
      content: '出错了 请联系管理!',
      showCancel:false,
      complete: (res) => {
        if (res.confirm) {
          this.lxkf()
        }
      }
    })
  },
  gxgn(){

    r('',{jz:'gxgn',user:wx.getStorageSync("data").openid},'POST','','').then((res)=>{
      var data = jie(res.data)
      console.log(data);
       if(data.code==1){
        if(wx.getStorageSync("cs")!=''){
          var cs = wx.getStorageSync("cs")+1
        }else{
          var cs = 1
        }
        wx.setStorageSync('cs',cs)
        this.onSaveToPhone()
       }else{
        this.lxkf()
       }
    })

  },



  onHide(){
    if(this.data.qz==true){
      this.setData({index:1})
    }
  },



  kgg(){
    if (videoAd) {
      videoAd.show().catch(() => {
        videoAd.load()
          .then(() => videoAd.show())
          .catch(err => {
            console.log('激励视频 广告显示失败')
          })
      })
    }

  },

  zx(){

    if(wx.getStorageSync("data").kg==1){

      if(wx.getStorageSync("cs")>wx.getStorageSync("data").bl){
        this.setData({
          qz:true
        })
        this.kgg()
        console.log(1);
      }else{
        this.setData({
          qz:false
        })
        this.kgg()
        console.log(2);
      }

    }else{
       
      this.setData({
        qz:false
      })
      this.kgg()
      console.log(3);
    }


  },

  hqid(){
    if(wx.getStorageSync("data").kg==1){
      wx.showModal({
        title: '提示',
        content: '保存表情包 需观看一个广告o',
        cancelText:'不要啦',
        confirmText:'立即观看',
        confirmColor:'#ff9988',
        complete: (res) => {
          if (res.confirm) {
            this.zx()
          }
          if (res.cancel) {
            wx.showToast({
              title: '保存失败',
              icon:'none'
            })
          }
        }
      })
    }else{
      this.gxgn()
    }
  },



  onSaveToPhone() {
    this.getSetting().then((res) => {
      if (!res.authSetting['scope.writePhotosAlbum']) {
        this.authorize().then(() => {
          wx.showToast({
            title: '正在保存中...',
            icon: 'loading',
            duration: 99999
          })
          this.savedownloadFile(this.data.img)
          this.setData({
            isAuthSavePhoto: false
          })
        }).catch(() => {
          wx.showToast({
            title: '您拒绝了授权',
            icon: 'none',
            duration: 1500
          })
          this.setData({
            isAuthSavePhoto: true
          })
        })
      } else {
        wx.showToast({
          title: '正在保存中...',
          icon: 'loading',
          duration: 99999
        })
        this.savedownloadFile(this.data.img)
      }
    })
  },
  //打开设置，引导用户授权
  onOpenSetting() {
    wx.openSetting({
      success:(res) => {
        console.log(res.authSetting)
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.showToast({
            title: '您未授权',
            icon: 'none',
            duration: 1500
          })
          this.setData({// 拒绝授权
            isAuthSavePhoto: true
          })
 
        } else {// 接受授权
          this.setData({
            isAuthSavePhoto: false
          })
          this.onSaveToPhone() // 接受授权后保存图片
 
        }
 
      }
    })
   
  },
  // 获取用户已经授予了哪些权限
  getSetting() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: res => {
          resolve(res)
        }
      })
    })
  },
  // 发起首次授权请求
  authorize() {
    return new Promise((resolve, reject) => {
      wx.authorize({
        scope: 'scope.writePhotosAlbum',
        success: () => {
          resolve()
        },
        fail: res => { //这里是用户拒绝授权后的回调
          console.log('拒绝授权')
          reject()
        }
      })
    })
  },
  savedownloadFile(img) {
    this.downLoadFile(img).then((res) => {
      return this.saveImageToPhotosAlbum(res.tempFilePath)
    }).then(() => {      
    })
  },
  //单文件下载(下载文件资源到本地)，客户端直接发起一个 HTTPS GET 请求，返回文件的本地临时路径。
  downLoadFile(img) {
    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: img,
        success: (res) => {
          console.log('downloadfile', res)
          resolve(res)
        }
      })
    })
  },
  // 保存图片到系统相册
  saveImageToPhotosAlbum(saveUrl) {
    return new Promise((resolve, reject) => {
      wx.saveImageToPhotosAlbum({
        filePath: saveUrl,
        success: (res) => {
          wx.showToast({
            title: '保存成功',
            duration: 1000,
          })
          resolve()
        }
      })
    })
  },
  // 弹出模态框提示用户是否要去设置页授权
  showModal(){
    wx.showModal({
      title: '检测到您没有打开保存到相册的权限，是否前往设置打开？',
      success: (res) => {
        if (res.confirm) {
          console.log('用户点击确定')
          this.onOpenSetting() // 打开设置页面          
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  },
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})