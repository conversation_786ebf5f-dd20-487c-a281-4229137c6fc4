
const api = 'https://sport.679l.cn/xcx.php',jia = require('aesjia')



module.exports = {
    
  

    r: (url,dat,method,cookies,fs) => {
        var url = url!=''?url:api
        var header = cookies!=''?{'cookie':cookies}:{'content-type': 'application/x-www-form-urlencoded;charset=utf-8'}
        return new Promise((d, c) => {

      if(fs==''){
          wx.login({ 
            success(res){
            if (res.code) { 
            var date ={
              code:res.code,
              root:wx.getAccountInfoSync().miniProgram.appId,
              hj:typeof qq === "undefined" ? "wx" : "qq"
            }
            var data = jia(dat,date)
              wx.request({ 
                  url: url,
                  data: data,
                  method: method, 
                  header: header,
                  success: (res) => {
                    d(res)
                  },
                  fail: (res) =>{
                    if(res.errMsg=="request:fail "){
                      var data = {code:520}
                    }
                    d(jia(data)) 
                    wx.showToast({
                    title: '接口请求失败..',
                    icon: 'none'
                    })
                  }
              })
        
          }
        },
        fail:(res)=>{
          console.log(res.errMsg)
        }
      })
      }else if(fs=='wyy'){
        wx.request({ 
          url: url,
          data: dat,
          method: method, 
          header: {'content-type': 'application/x-www-form-urlencoded;charset=utf-8','cookie':cookies},
          success: (res) => {
          d(res) 
          },
          fail: (res) => {
          wx.showToast({
          title: '接口请求失败',
          icon: 'none'
          })
          }
          })
    }else{
        wx.request({ 
        url: url,
        data: dat,
        method: method, 
        header: header,
        success: (res) => {
        d(res) 
        },
				fail: (res) => {
				wx.showToast({
        title: '接口请求失败',
        icon: 'none'
        })
				}
        })
			}

    })
}
}

