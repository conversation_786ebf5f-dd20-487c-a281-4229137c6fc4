Page({
    data: {
        ip: "",
        show: !1,
        getdata: {}
    },
    onShareAppMessage: function() {},
    ip: function(t) {
        this.setData({
            ip: t.detail.value
        });
    },
    getdata: function(t) {
        var a = this;
        wx.showLoading({
            title: "加载中"
        });
        var e = this.data.ip;
        if ("" == e) return wx.showToast({
            title: "输入不合法",
            icon: "none",
            image: "../../img/error.png",
            duration: 2e3
        }), !1;
        wx.request({
            url: "https://apis.juhe.cn/ip/ipNew?ip=" + e + "&key=9a77a7c215a0b4dbdf2854fd2e02202c",
            data: {},
            header: {
                "Content-Type": "application/json"
            },
            success: function(t) {
                wx.hideLoading(), "200" == t.data.resultcode ? a.setData({
                    show: !0,
                    getdata: t.data.result
                }) : wx.showModal({
                    title: "提示",
                    content: "查询失败",
                    success: function(t) {
                        a.setData({
                            show: !1
                        });
                    }
                });
            }
        });
    }
});