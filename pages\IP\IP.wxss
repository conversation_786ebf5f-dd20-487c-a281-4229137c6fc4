page {
    background-color: #f7f7f7;
}
.pages-box {
    padding: 30rpx;
    background-color: #fff;
    border-radius: 10rpx;
    box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(0,0,0,.05);
}

.inp-box {
    padding-top: 30rpx;
    position: relative;
}
.inp-box,.pages-box {
    box-sizing: border-box;
}
.ip-box,page {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

.ip-box {
    padding: 30rpx;
}

.fc99 {
    color: #999;
}
.fs32 {
    font-size: 32rpx;
}

.fs36 {
    font-size: 36rpx;
}

.inp {
    border: 3rpx solid #3677f0;
    height: 80rpx;
    border-radius: 10rpx;
    line-height: 80rpx;
    box-sizing: border-box;
    padding-left: 130rpx;
}

.inpimg {
    width: 120rpx;
    height: 74rpx;
    background-color: #f1f2f6;
    border-top-left-radius: 10rpx;
    border-bottom-left-radius: 10rpx;
    position: absolute;
    top: 33rpx;
    left: 3rpx;
}

.pagesimg {
    width: 40rpx;
    height: 40rpx;
    margin-top: 17rpx;
    margin-left: 38rpx;
}

.btn-box {
    margin-top: 30rpx;
    margin-bottom: 30rpx;
}

.btn,.btn-box {
    text-align: center;
}

.btn {
    width: 200rpx;
    background-color: #3677f0;
    color: #fff;
}
.det-box {
    margin-top: 20rpx;
    box-sizing: border-box;
    padding: 0rpx 30rpx;
    background-color: #fff;
    border-radius: 10rpx;
    box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(0,0,0,.05);
}

.pg-t,.pglist {
    padding: 20rpx 0rpx;
    border-bottom: 1rpx solid #f0f0f0;
}
.pg-l {
    width: 30%;
}

.pglist {
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

.pg-l,.pg-r {
    display: inline-block;
}

.pg-r {
    width: 70%;
}

.bd-n {
    border: none;
}