.content {
  height: 100%;
  padding-bottom: 80px;
  padding-top: 250rpx;
}

.Top_Nav {
  backdrop-filter: blur(40px);
  background-color: initial;
  background-color: #fff;
  background-color: hsla(0,0%,100%,.8);
}

.App_Title_text {
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 1px;
}

.Logo {
  border-radius: 4px;
  height: 50px;
  width: 50px;
}

.App_Tips {
  color: #767676;
  font-size: 8.5px;
  margin-top: 2px;
}

.Class_box {
  margin-top: 10px;
  white-space: nowrap;
}

.Class_box_ul {
  display: inline-block;
  margin-right: 10px;
  
}

.Class_box_li {
  border-radius: 14px;
  height: 100%;
  padding: 2px;
  width: 100%;
}

.Class_box_ul_text {
  color: #989898;
  font-size: 12px;
  font-weight: 700;
  padding: 0 16rpx;
}

.Class_box_ul_text_hove {
  color: #fff!important;
}

.Class_box_li_hove {
  background-color: #2ea4aa;
}

.image_box {
  height: 230rpx;
  width: 32%;
}

.image_box,.image_box1 {
  margin-top: 8px;
  position: relative;
}

.image_box1 {
  height: 345rpx;
  width: 49%;
}

.image_box_image {
  border-radius: 5px;
  height: 100%;
  width: 100%;
}

.hot_count_box {
  background-color: rgba(0,0,0,.36);
  border-radius: 10px;
  bottom: 5px;
  left: 5px;
  padding: 2px 4px;
  position: absolute;
  z-index: 1;
}

.hot_count_box_ico {
  height: 10px;
  width: 10px;
}

.hot_count_box_text {
  color: #f5f5f5;
  font-size: 8px;
  margin-bottom: -1px;
  margin-left: 2px;
}



page {
background-color: #ffffff;
}

.paddingbj10 {
padding-left: 10rpx;
padding-right: 10rpx;
}

.paddingbj {
padding-left: 20rpx;
padding-right: 20rpx;
}

.content,.index {
width: 100%;
}

.index {
background-color: #fdfdfd;
}

.bfb {
width: 100%;
}

.display {
display: flex;
}

.flexbfb {
flex: 1;
}

.jiushiliu {
width: 96%;
}

.jiushi {
width: 90%;
}

.jiushisi {
width: 94%;
}

.jiushiba {
width: 98%;
}

.displayflexrow {
display: flex;
flex-direction: row;
}

.flexrowcenter {
justify-content: center;
}

.flexrowbetween,.flexrowcenter {
align-items: center;
display: flex;
flex-direction: row;
}

.flexrowbetween {
flex-wrap: wrap;
justify-content: space-between;
}

.flexrowtopbetween {
align-items: flex-start;
justify-content: space-between;
}

.flexrowstart,.flexrowtopbetween {
display: flex;
flex-direction: row;
}

.flexrowstart {
align-items: center;
justify-content: flex-start;
}

.flexrowleftstart,.flexrowleftstartwrap {
align-items: center;
display: flex;
flex-direction: row;
justify-content: flex-start;
}

.flexrowleftstartwrap {
flex-wrap: wrap;
}

.flexrowbetweenwrap {
flex-wrap: wrap;
justify-content: space-between;
}

.flexrowbetweenwrap,.flexrowrightstart {
align-items: center;
display: flex;
flex-direction: row;
}

.flexrowrightstart {
justify-content: flex-end;
}

.flexrowrightstarttop,.flexrowrighttop {
align-items: flex-start;
}

.flexrowrightbottom,.flexrowrightstarttop,.flexrowrighttop {
display: flex;
flex-direction: row;
justify-content: flex-end;
}

.flexrowrightbottom {
align-items: flex-end;
}

.flexrowleftbottom {
align-items: flex-end;
}

.flexrowleftbottom,.flexrowlefttop {
display: flex;
flex-direction: row;
justify-content: flex-start;
}

.flexrowlefttop {
align-items: flex-start;
}

.flexrowtopcenter {
justify-content: center;
}

.flexrowrighttopstart,.flexrowtopcenter {
align-items: flex-start;
display: flex;
flex-direction: row;
}

.flexrowrighttopstart {
justify-content: flex-end;
}

.flexrowbottomcenter {
justify-content: center;
}

.flexrowbottombetween,.flexrowbottomcenter {
align-items: flex-end;
display: flex;
flex-direction: row;
}

.flexrowbottombetween {
justify-content: space-between;
}

.flexrowend {
align-items: center;
display: flex;
flex-direction: row;
justify-content: flex-end;
}

.flexcolumnleftcenter,.flexcolumnunll {
display: flex;
flex-direction: column;
}

.flexcolumnleftcenter {
align-items: flex-start;
justify-content: center;
}

.flexcolumnbetween {
justify-content: space-between;
}

.flexcolumnbetween,.flexcolumnrigthcenter {
display: flex;
flex-direction: column;
}

.flexcolumnrigthcenter {
align-items: flex-end;
justify-content: center;
}

.flexcolumlefttop {
align-items: flex-start;
}

.flexcolumlefttop,.flexcolumrigthtop {
display: flex;
flex-direction: column;
justify-content: flex-start;
}

.flexcolumrigthtop {
align-items: flex-end;
}

.flexcolumn {
align-items: flex-start;
display: flex;
flex-direction: column;
justify-content: center;
}

.flexcolumncenterstart,.flexcolumncentertop {
justify-content: flex-start;
}

.flexcolumnbetweencenter,.flexcolumncenterstart,.flexcolumncentertop {
align-items: center;
display: flex;
flex-direction: column;
}

.flexcolumnbetweencenter {
justify-content: space-between;
}

.flexcolumncenter {
justify-content: center;
}

.flexcolumnaligncenter,.flexcolumncenter {
align-items: center;
display: flex;
flex-direction: column;
}

.flexrowbetweencenter {
align-items: flex-end;
display: flex;
flex-direction: row;
justify-content: space-between;
}

button {
background-color: initial;
border-radius: 0rpx;
display: inline-block;
line-height: 1;
margin: 0rpx;
padding: 0rpx;
}

button::after {
border: none;
}

.button-hover {
background-color: initial;
color: #000;
}

[bind-data-custom-hidden="true"],[data-custom-hidden="true"] {
display: none!important;
}


.scroll-wrapper {
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  background: #FFF;
  height: 90rpx;
  padding: 0 32rpx;
  box-sizing: border-box;
}
/* 去掉滚动条 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.navigate-item {
  display: inline-block;
  text-align: center;
  height: 90rpx;
  line-height: 90rpx;
  margin: 0 30rpx;
}

.names {
  font-size: 28rpx;
  color: #3c3c3c;
}

.names.active {
  color: #db7c22;
  font-weight: bold;
  font-size: 34rpx;
}

.currtline {
  margin: -8rpx auto 0 auto;
  width: 100rpx;
  height: 8rpx;
  border-radius: 4rpx;
}

.currtline.active {
  background: #db7c22;
  transition: all .3s;
}
.tab_title{
  margin: 20rpx;
  border: 1px solid #db7c22;
  padding: 20rpx;
  box-sizing: border-box;
}

.swiper-item {
  width: 100%;
  padding: 32rpx ;
  box-sizing: border-box;
}





.ajaxloader1 {
  width: 30px;
  height: 30px;
  border: 8px solid #fff;
  border-radius: 50%;
  -webkit-box-shadow: 0 0 25px 2px;
  -moz-box-shadow: 0 0 25px 2px;
  box-shadow: 0 0 25px 2px;
  color: #fff;
  border-color: red;
  color: red;
  border-right-color: transparent;
  border-top-color: transparent;
  -webkit-animation: spin-right 1s linear infinite normal;
  -moz-animation: spin-right 1s linear infinite normal;
  -ms-animation: spin-right 1s linear infinite normal;
  animation: spin-right 1s linear infinite normal;
  -webkit-animation-delay: 0;
  -moz-animation-delay: 0;
  -o-animation-delay: 0;
  animation-delay: 0;
  margin: 30px auto 0;
}



