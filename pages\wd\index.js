const {r} = require("../../libs/ajax.js")
var jie = require('../../libs/aesjie');
Page({

  data: {

  },

  onLoad(options) {
    r('',{jz:'index'},'POST','','').then((res)=>{
      var data = jie(res.data)
      console.log(data)
      if(data.code==1){
        wx.setStorageSync('data',data)
        this.setData({id:data.openid})
      }
    })
  },

  gkcg(){
    wx.setClipboardData({
      data: wx.getStorageSync("data").openid,
      success: function (res) {
        wx.showToast({ title: '复制成功',icon:'none'})
      }
    })
  },

  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})