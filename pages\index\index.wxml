<view class="Top_Nav" style="height: {{gd+5}}px;display: flex;padding-top:{{db}}px;background-color: hsla(0,0%,100%,.8);backdrop-filter: blur(40px);background-color: initial;position: fixed;z-index: 9999999;top: 0;width: 100%;">
  
      
      <view wx:if="{{loading!=0}}" style="display: flex;width: 100%;font-weight: 700;align-items: center;margin-top: -5px;padding-left: {{zy+zy}}px;">
      <view style="margin-right: 10rpx;" wx:for="{{NavTop}}" wx:key="index">
      <view bindtap="tabNav" data-current="{{item.id}}" style="border-radius: 30px;font-size: 24rpx;display: flex;flex-direction: column;padding: 6rpx 25rpx;{{TagID==item.tag_id?'background-color: #2ea4aa;color: #fff;':''}}">
      <view >{{item.title}}</view>
      </view>
      </view>
      
      </view>

</view>



<swiper style="padding-top:{{db+gd}}px;min-height: calc(100vh - {{db+gd+25}}px);" bindchange="handleSwiper" current="{{currentTab}}">
	<block wx:for="{{NavTop}}" wx:key="index">
			<swiper-item>
				<scroll-view scroll-y bindscrolltolower="onReachBottom" style="padding: 0rpx 0rpx 30rpx;margin-left: 3%;height: 100%;">
				
          <view class="flexrowbetweenwrap jiushisi">
          <view class="image_box" wx:for="{{List}}" wx:key="index">
          <image bindtap="ck" class="image_box_image" data-id="{{item.id}}" mode="aspectFill" src="{{item.tp}}"></image>
          </view>
          </view>

        
        </scroll-view>
			</swiper-item>
	</block>
</swiper>