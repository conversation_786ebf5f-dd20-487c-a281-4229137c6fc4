// 首页 - 表情包列表
const api = require('../../utils/api')

Page({
  data: {
    categories: [
      { id: 1, name: '热门', icon: '🔥' },
      { id: 2, name: '搞笑', icon: '😂' },
      { id: 3, name: '可爱', icon: '🥰' },
      { id: 4, name: '表情', icon: '😊' },
      { id: 5, name: '动物', icon: '🐱' },
      { id: 6, name: '卡通', icon: '🎭' }
    ],
    currentCategory: 1,
    emojiList: [],
    loading: false,
    page: 1,
    hasMore: true
  },

  onLoad() {
    this.loadEmojiList()
  },

  onShow() {
    // 页面显示时刷新数据
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  onPullDownRefresh() {
    this.data.page = 1
    this.data.hasMore = true
    this.loadEmojiList(true)
  },

  // 切换分类
  switchCategory(e) {
    const categoryId = e.currentTarget.dataset.id
    if (categoryId === this.data.currentCategory) return

    this.setData({
      currentCategory: categoryId,
      page: 1,
      hasMore: true,
      emojiList: []
    })
    this.loadEmojiList()
  },

  // 加载表情包列表
  async loadEmojiList(refresh = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const result = await api.getEmojiList({
        category: this.data.currentCategory,
        page: this.data.page,
        limit: 20
      })

      if (result.success) {
        const newList = refresh ? result.data : [...this.data.emojiList, ...result.data]
        
        this.setData({
          emojiList: newList,
          hasMore: result.data.length >= 20,
          loading: false
        })

        if (refresh) {
          wx.stopPullDownRefresh()
        }
      } else {
        throw new Error(result.message || '加载失败')
      }
    } catch (error) {
      console.error('加载表情包失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({ loading: false })
    }
  },

  // 加载更多
  loadMore() {
    this.setData({
      page: this.data.page + 1
    })
    this.loadEmojiList()
  },

  // 预览表情包
  previewEmoji(e) {
    const { url, index } = e.currentTarget.dataset
    const urls = this.data.emojiList.map(item => item.url)
    
    wx.previewImage({
      current: url,
      urls: urls
    })
  },

  // 查看详情
  viewDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 保存表情包
  async saveEmoji(e) {
    const { url, name } = e.currentTarget.dataset
    
    try {
      // 显示加载提示
      wx.showLoading({
        title: '保存中...'
      })

      // 下载图片
      const downloadResult = await this.downloadImage(url)
      
      // 保存到相册
      await this.saveToAlbum(downloadResult.tempFilePath)
      
      wx.hideLoading()
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })

      // 记录保存行为
      this.recordSaveAction(name)

    } catch (error) {
      wx.hideLoading()
      console.error('保存失败:', error)
      
      if (error.errMsg && error.errMsg.includes('auth')) {
        this.requestAlbumAuth()
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    }
  },

  // 下载图片
  downloadImage(url) {
    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: url,
        success: resolve,
        fail: reject
      })
    })
  },

  // 保存到相册
  saveToAlbum(filePath) {
    return new Promise((resolve, reject) => {
      wx.saveImageToPhotosAlbum({
        filePath: filePath,
        success: resolve,
        fail: reject
      })
    })
  },

  // 请求相册权限
  requestAlbumAuth() {
    wx.showModal({
      title: '需要相册权限',
      content: '保存表情包需要访问您的相册，请在设置中开启权限',
      confirmText: '去设置',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting()
        }
      }
    })
  },

  // 记录保存行为
  recordSaveAction(name) {
    // 可以在这里添加统计代码
    console.log('用户保存了表情包:', name)
  }
})
